#pragma once

// @generated by torchgen/gen.py from Function.h

#include <ATen/Context.h>
#include <ATen/DeviceGuard.h>
#include <ATen/TensorUtils.h>
#include <ATen/TracerMode.h>
#include <ATen/core/Generator.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <string_view>



#include <ATen/ops/_fused_rms_norm_ops.h>

namespace at {


// aten::_fused_rms_norm(Tensor input, int normalized_shape_ndim, Tensor weight, float eps) -> Tensor
inline at::Tensor _fused_rms_norm(const at::Tensor & input, int64_t normalized_shape_ndim, const at::Tensor & weight, double eps) {
    return at::_ops::_fused_rms_norm::call(input, normalized_shape_ndim, weight, eps);
}

}
