#pragma once

// an external backend might generate file within its code tree
// and check all the source files within the tree with clang-format.
// so, disable it since the backend might have a different config.
// clang-format off

// Autogenerated file by gen_backend_stubs.py. Do not edit directly!

#include <ATen/Tensor.h>

namespace torch {
namespace lazy {

struct LazyNativeFunctions {

static ::std::tuple<at::Tensor,at::Tensor,at::Tensor> convolution_backward(const at::Tensor & grad_output, const at::Tensor & input, const at::Tensor & weight, at::OptionalIntArrayRef bias_sizes, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool transposed, at::IntArrayRef output_padding, int64_t groups, ::std::array<bool,3> output_mask);
static ::std::tuple<at::Tensor,at::Tensor,at::Tensor> native_batch_norm(const at::Tensor & input, const ::std::optional<at::Tensor> & weight, const ::std::optional<at::Tensor> & bias, const ::std::optional<at::Tensor> & running_mean, const ::std::optional<at::Tensor> & running_var, bool training, double momentum, double eps);
static ::std::tuple<at::Tensor,at::Tensor,at::Tensor> native_batch_norm_backward(const at::Tensor & grad_out, const at::Tensor & input, const ::std::optional<at::Tensor> & weight, const ::std::optional<at::Tensor> & running_mean, const ::std::optional<at::Tensor> & running_var, const ::std::optional<at::Tensor> & save_mean, const ::std::optional<at::Tensor> & save_invstd, bool train, double eps, ::std::array<bool,3> output_mask);
static ::std::tuple<at::Tensor,at::Tensor,at::Tensor> native_layer_norm(const at::Tensor & input, at::IntArrayRef normalized_shape, const ::std::optional<at::Tensor> & weight, const ::std::optional<at::Tensor> & bias, double eps);
static ::std::tuple<at::Tensor,at::Tensor,at::Tensor> native_layer_norm_backward(const at::Tensor & grad_out, const at::Tensor & input, at::IntArrayRef normalized_shape, const at::Tensor & mean, const at::Tensor & rstd, const ::std::optional<at::Tensor> & weight, const ::std::optional<at::Tensor> & bias, ::std::array<bool,3> output_mask);
static ::std::tuple<at::Tensor,at::Tensor> grid_sampler_2d_backward(const at::Tensor & grad_output, const at::Tensor & input, const at::Tensor & grid, int64_t interpolation_mode, int64_t padding_mode, bool align_corners, ::std::array<bool,2> output_mask);
static ::std::tuple<at::Tensor,at::Tensor> log_sigmoid_forward(const at::Tensor & self);
static ::std::tuple<at::Tensor,at::Tensor> max(const at::Tensor & self, int64_t dim, bool keepdim);
static ::std::tuple<at::Tensor,at::Tensor> max_pool2d_with_indices(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool ceil_mode);
static ::std::tuple<at::Tensor,at::Tensor> max_pool3d_with_indices(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool ceil_mode);
static ::std::tuple<at::Tensor,at::Tensor> native_dropout(const at::Tensor & input, double p, ::std::optional<bool> train);
static ::std::tuple<at::Tensor,at::Tensor> nll_loss2d_forward(const at::Tensor & self, const at::Tensor & target, const ::std::optional<at::Tensor> & weight, int64_t reduction, int64_t ignore_index);
static ::std::tuple<at::Tensor,at::Tensor> nll_loss_forward(const at::Tensor & self, const at::Tensor & target, const ::std::optional<at::Tensor> & weight, int64_t reduction, int64_t ignore_index);
static ::std::tuple<at::Tensor,at::Tensor> sort(const at::Tensor & self, int64_t dim, bool descending);
static ::std::tuple<at::Tensor,at::Tensor> topk(const at::Tensor & self, int64_t k, int64_t dim, bool largest, bool sorted);
static at::Tensor & arange_out(const at::Scalar & start, const at::Scalar & end, const at::Scalar & step, at::Tensor & out);
static at::Tensor & fill_(at::Tensor & self, const at::Scalar & value);
static at::Tensor & logsumexp_out(const at::Tensor & self, at::IntArrayRef dim, bool keepdim, at::Tensor & out);
static at::Tensor _adaptive_avg_pool2d(const at::Tensor & self, at::IntArrayRef output_size);
static at::Tensor _adaptive_avg_pool2d_backward(const at::Tensor & grad_output, const at::Tensor & self);
static at::Tensor _copy_from(const at::Tensor & self, const at::Tensor & dst, bool non_blocking);
static at::Tensor _copy_from_and_resize(const at::Tensor & self, const at::Tensor & dst);
static at::Tensor _log_softmax(const at::Tensor & self, int64_t dim, bool half_to_float);
static at::Tensor _log_softmax_backward_data(const at::Tensor & grad_output, const at::Tensor & output, int64_t dim, at::ScalarType input_dtype);
static at::Tensor _reshape_alias_copy_symint(const at::Tensor & self, c10::SymIntArrayRef size, c10::SymIntArrayRef stride);
static at::Tensor _softmax(const at::Tensor & self, int64_t dim, bool half_to_float);
static at::Tensor _softmax_backward_data(const at::Tensor & grad_output, const at::Tensor & output, int64_t dim, at::ScalarType input_dtype);
static at::Tensor _to_copy(const at::Tensor & self, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory, bool non_blocking, ::std::optional<at::MemoryFormat> memory_format);
static at::Tensor _trilinear(const at::Tensor & i1, const at::Tensor & i2, const at::Tensor & i3, at::IntArrayRef expand1, at::IntArrayRef expand2, at::IntArrayRef expand3, at::IntArrayRef sumdim, int64_t unroll_dim);
static at::Tensor _unsafe_view(const at::Tensor & self, at::IntArrayRef size);
static at::Tensor abs(const at::Tensor & self);
static at::Tensor add(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha);
static at::Tensor addcdiv(const at::Tensor & self, const at::Tensor & tensor1, const at::Tensor & tensor2, const at::Scalar & value);
static at::Tensor addcmul(const at::Tensor & self, const at::Tensor & tensor1, const at::Tensor & tensor2, const at::Scalar & value);
static at::Tensor addmm(const at::Tensor & self, const at::Tensor & mat1, const at::Tensor & mat2, const at::Scalar & beta, const at::Scalar & alpha);
static at::Tensor alias_copy(const at::Tensor & self);
static at::Tensor all(const at::Tensor & self);
static at::Tensor any(const at::Tensor & self);
static at::Tensor as_strided_copy_symint(const at::Tensor & self, c10::SymIntArrayRef size, c10::SymIntArrayRef stride, ::std::optional<c10::SymInt> storage_offset);
static at::Tensor as_strided_scatter_symint(const at::Tensor & self, const at::Tensor & src, c10::SymIntArrayRef size, c10::SymIntArrayRef stride, ::std::optional<c10::SymInt> storage_offset);
static at::Tensor avg_pool2d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, bool ceil_mode, bool count_include_pad, ::std::optional<int64_t> divisor_override);
static at::Tensor avg_pool2d_backward(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, bool ceil_mode, bool count_include_pad, ::std::optional<int64_t> divisor_override);
static at::Tensor baddbmm(const at::Tensor & self, const at::Tensor & batch1, const at::Tensor & batch2, const at::Scalar & beta, const at::Scalar & alpha);
static at::Tensor bernoulli(const at::Tensor & self, ::std::optional<at::Generator> generator);
static at::Tensor bernoulli(const at::Tensor & self, double p, ::std::optional<at::Generator> generator);
static at::Tensor binary_cross_entropy(const at::Tensor & self, const at::Tensor & target, const ::std::optional<at::Tensor> & weight, int64_t reduction);
static at::Tensor binary_cross_entropy_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, const ::std::optional<at::Tensor> & weight, int64_t reduction);
static at::Tensor bitwise_and(const at::Tensor & self, const at::Tensor & other);
static at::Tensor bitwise_or(const at::Tensor & self, const at::Tensor & other);
static at::Tensor block_diag(at::TensorList tensors);
static at::Tensor bmm(const at::Tensor & self, const at::Tensor & mat2);
static at::Tensor cat(const at::ITensorListRef & tensors, int64_t dim);
static at::Tensor clamp(const at::Tensor & self, const ::std::optional<at::Scalar> & min, const ::std::optional<at::Scalar> & max);
static at::Tensor clamp_min(const at::Tensor & self, const at::Scalar & min);
static at::Tensor clone(const at::Tensor & self, ::std::optional<at::MemoryFormat> memory_format);
static at::Tensor constant_pad_nd(const at::Tensor & self, at::IntArrayRef pad, const at::Scalar & value);
static at::Tensor convolution(const at::Tensor & input, const at::Tensor & weight, const ::std::optional<at::Tensor> & bias, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool transposed, at::IntArrayRef output_padding, int64_t groups);
static at::Tensor cos(const at::Tensor & self);
static at::Tensor cumsum(const at::Tensor & self, int64_t dim, ::std::optional<at::ScalarType> dtype);
static at::Tensor detach_copy(const at::Tensor & self);
static at::Tensor diag_embed(const at::Tensor & self, int64_t offset, int64_t dim1, int64_t dim2);
static at::Tensor diagonal_backward_symint(const at::Tensor & grad_output, c10::SymIntArrayRef input_sizes, int64_t offset, int64_t dim1, int64_t dim2);
static at::Tensor diagonal_copy(const at::Tensor & self, int64_t offset, int64_t dim1, int64_t dim2);
static at::Tensor diagonal_scatter(const at::Tensor & self, const at::Tensor & src, int64_t offset, int64_t dim1, int64_t dim2);
static at::Tensor div(const at::Tensor & self, const at::Tensor & other);
static at::Tensor div(const at::Tensor & self, const at::Tensor & other, ::std::optional<c10::string_view> rounding_mode);
static at::Tensor elu(const at::Tensor & self, const at::Scalar & alpha, const at::Scalar & scale, const at::Scalar & input_scale);
static at::Tensor elu_backward(const at::Tensor & grad_output, const at::Scalar & alpha, const at::Scalar & scale, const at::Scalar & input_scale, bool is_result, const at::Tensor & self_or_result);
static at::Tensor embedding(const at::Tensor & weight, const at::Tensor & indices, int64_t padding_idx, bool scale_grad_by_freq, bool sparse);
static at::Tensor embedding_dense_backward(const at::Tensor & grad_output, const at::Tensor & indices, int64_t num_weights, int64_t padding_idx, bool scale_grad_by_freq);
static at::Tensor empty_strided_symint(c10::SymIntArrayRef size, c10::SymIntArrayRef stride, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory);
static at::Tensor empty_symint(c10::SymIntArrayRef size, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory, ::std::optional<at::MemoryFormat> memory_format);
static at::Tensor eq(const at::Tensor & self, const at::Scalar & other);
static at::Tensor eq(const at::Tensor & self, const at::Tensor & other);
static at::Tensor exp(const at::Tensor & self);
static at::Tensor expand_copy_symint(const at::Tensor & self, c10::SymIntArrayRef size, bool implicit);
static at::Tensor flip(const at::Tensor & self, at::IntArrayRef dims);
static at::Tensor floor(const at::Tensor & self);
static at::Tensor frac(const at::Tensor & self);
static at::Tensor gather(const at::Tensor & self, int64_t dim, const at::Tensor & index, bool sparse_grad);
static at::Tensor ge(const at::Tensor & self, const at::Scalar & other);
static at::Tensor ge(const at::Tensor & self, const at::Tensor & other);
static at::Tensor gelu(const at::Tensor & self, c10::string_view approximate);
static at::Tensor gelu_backward(const at::Tensor & grad_output, const at::Tensor & self, c10::string_view approximate);
static at::Tensor glu(const at::Tensor & self, int64_t dim);
static at::Tensor glu_backward(const at::Tensor & grad_output, const at::Tensor & self, int64_t dim);
static at::Tensor glu_jvp(const at::Tensor & glu, const at::Tensor & x, const at::Tensor & dx, int64_t dim);
static at::Tensor grid_sampler_2d(const at::Tensor & input, const at::Tensor & grid, int64_t interpolation_mode, int64_t padding_mode, bool align_corners);
static at::Tensor gt(const at::Tensor & self, const at::Scalar & other);
static at::Tensor gt(const at::Tensor & self, const at::Tensor & other);
static at::Tensor hardsigmoid(const at::Tensor & self);
static at::Tensor index_select(const at::Tensor & self, int64_t dim, const at::Tensor & index);
static at::Tensor le(const at::Tensor & self, const at::Scalar & other);
static at::Tensor le(const at::Tensor & self, const at::Tensor & other);
static at::Tensor leaky_relu(const at::Tensor & self, const at::Scalar & negative_slope);
static at::Tensor leaky_relu_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Scalar & negative_slope, bool self_is_result);
static at::Tensor lift(const at::Tensor & self);
static at::Tensor lift_fresh(const at::Tensor & self);
static at::Tensor linalg_pinv(const at::Tensor & self, const ::std::optional<at::Tensor> & atol, const ::std::optional<at::Tensor> & rtol, bool hermitian);
static at::Tensor log(const at::Tensor & self);
static at::Tensor log2(const at::Tensor & self);
static at::Tensor log_sigmoid_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & buffer);
static at::Tensor logdet(const at::Tensor & self);
static at::Tensor lt(const at::Tensor & self, const at::Scalar & other);
static at::Tensor lt(const at::Tensor & self, const at::Tensor & other);
static at::Tensor masked_fill(const at::Tensor & self, const at::Tensor & mask, const at::Scalar & value);
static at::Tensor masked_fill(const at::Tensor & self, const at::Tensor & mask, const at::Tensor & value);
static at::Tensor max(const at::Tensor & self);
static at::Tensor max_pool2d_with_indices_backward(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool ceil_mode, const at::Tensor & indices);
static at::Tensor max_pool3d_with_indices_backward(const at::Tensor & grad_output, const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool ceil_mode, const at::Tensor & indices);
static at::Tensor maximum(const at::Tensor & self, const at::Tensor & other);
static at::Tensor mean(const at::Tensor & self, ::std::optional<at::ScalarType> dtype);
static at::Tensor mean(const at::Tensor & self, at::OptionalIntArrayRef dim, bool keepdim, ::std::optional<at::ScalarType> dtype);
static at::Tensor min(const at::Tensor & self);
static at::Tensor minimum(const at::Tensor & self, const at::Tensor & other);
static at::Tensor mm(const at::Tensor & self, const at::Tensor & mat2);
static at::Tensor mul(const at::Tensor & self, const at::Tensor & other);
static at::Tensor mv(const at::Tensor & self, const at::Tensor & vec);
static at::Tensor narrow_copy_symint(const at::Tensor & self, int64_t dim, c10::SymInt start, c10::SymInt length);
static at::Tensor native_dropout_backward(const at::Tensor & grad_output, const at::Tensor & mask, double scale);
static at::Tensor ne(const at::Tensor & self, const at::Scalar & other);
static at::Tensor ne(const at::Tensor & self, const at::Tensor & other);
static at::Tensor neg(const at::Tensor & self);
static at::Tensor new_empty_strided_symint(const at::Tensor & self, c10::SymIntArrayRef size, c10::SymIntArrayRef stride, ::std::optional<at::ScalarType> dtype, ::std::optional<at::Layout> layout, ::std::optional<at::Device> device, ::std::optional<bool> pin_memory);
static at::Tensor nll_loss2d_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, const ::std::optional<at::Tensor> & weight, int64_t reduction, int64_t ignore_index, const at::Tensor & total_weight);
static at::Tensor nll_loss_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, const ::std::optional<at::Tensor> & weight, int64_t reduction, int64_t ignore_index, const at::Tensor & total_weight);
static at::Tensor nonzero(const at::Tensor & self);
static at::Tensor norm(const at::Tensor & self, const ::std::optional<at::Scalar> & p, at::IntArrayRef dim, bool keepdim);
static at::Tensor normal_functional(const at::Tensor & self, double mean, double std, ::std::optional<at::Generator> generator);
static at::Tensor permute_copy(const at::Tensor & self, at::IntArrayRef dims);
static at::Tensor pixel_shuffle(const at::Tensor & self, int64_t upscale_factor);
static at::Tensor pixel_unshuffle(const at::Tensor & self, int64_t downscale_factor);
static at::Tensor pow(const at::Tensor & self, const at::Scalar & exponent);
static at::Tensor pow(const at::Tensor & self, const at::Tensor & exponent);
static at::Tensor random(const at::Tensor & self, ::std::optional<at::Generator> generator);
static at::Tensor random(const at::Tensor & self, int64_t from, ::std::optional<int64_t> to, ::std::optional<at::Generator> generator);
static at::Tensor random(const at::Tensor & self, int64_t to, ::std::optional<at::Generator> generator);
static at::Tensor reciprocal(const at::Tensor & self);
static at::Tensor relu(const at::Tensor & self);
static at::Tensor remainder(const at::Tensor & self, const at::Tensor & other);
static at::Tensor repeat(const at::Tensor & self, at::IntArrayRef repeats);
static at::Tensor rsqrt(const at::Tensor & self);
static at::Tensor scatter_add(const at::Tensor & self, int64_t dim, const at::Tensor & index, const at::Tensor & src);
static at::Tensor select_backward_symint(const at::Tensor & grad_output, c10::SymIntArrayRef input_sizes, int64_t dim, c10::SymInt index);
static at::Tensor select_copy(const at::Tensor & self, int64_t dim, int64_t index);
static at::Tensor select_scatter(const at::Tensor & self, const at::Tensor & src, int64_t dim, int64_t index);
static at::Tensor sgn(const at::Tensor & self);
static at::Tensor sigmoid(const at::Tensor & self);
static at::Tensor sigmoid_backward(const at::Tensor & grad_output, const at::Tensor & output);
static at::Tensor silu(const at::Tensor & self);
static at::Tensor slice_backward_symint(const at::Tensor & grad_output, c10::SymIntArrayRef input_sizes, int64_t dim, c10::SymInt start, c10::SymInt end, c10::SymInt step);
static at::Tensor slice_copy_symint(const at::Tensor & self, int64_t dim, ::std::optional<c10::SymInt> start, ::std::optional<c10::SymInt> end, c10::SymInt step);
static at::Tensor slice_scatter_symint(const at::Tensor & self, const at::Tensor & src, int64_t dim, ::std::optional<c10::SymInt> start, ::std::optional<c10::SymInt> end, c10::SymInt step);
static at::Tensor smooth_l1_loss(const at::Tensor & self, const at::Tensor & target, int64_t reduction, double beta);
static at::Tensor smooth_l1_loss_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Tensor & target, int64_t reduction, double beta);
static at::Tensor softplus(const at::Tensor & self, const at::Scalar & beta, const at::Scalar & threshold);
static at::Tensor softplus_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Scalar & beta, const at::Scalar & threshold);
static at::Tensor sqrt(const at::Tensor & self);
static at::Tensor squeeze_copy(const at::Tensor & self);
static at::Tensor squeeze_copy(const at::Tensor & self, at::IntArrayRef dim);
static at::Tensor squeeze_copy(const at::Tensor & self, int64_t dim);
static at::Tensor stack(at::TensorList tensors, int64_t dim);
static at::Tensor std(const at::Tensor & self, at::OptionalIntArrayRef dim, bool unbiased, bool keepdim);
static at::Tensor std(const at::Tensor & self, at::OptionalIntArrayRef dim, const ::std::optional<at::Scalar> & correction, bool keepdim);
static at::Tensor std(const at::Tensor & self, bool unbiased);
static at::Tensor sub(const at::Tensor & self, const at::Tensor & other, const at::Scalar & alpha);
static at::Tensor sum(const at::Tensor & self, ::std::optional<at::ScalarType> dtype);
static at::Tensor sum(const at::Tensor & self, at::OptionalIntArrayRef dim, bool keepdim, ::std::optional<at::ScalarType> dtype);
static at::Tensor t_copy(const at::Tensor & self);
static at::Tensor tanh(const at::Tensor & self);
static at::Tensor tanh_backward(const at::Tensor & grad_output, const at::Tensor & output);
static at::Tensor threshold(const at::Tensor & self, const at::Scalar & threshold, const at::Scalar & value);
static at::Tensor threshold_backward(const at::Tensor & grad_output, const at::Tensor & self, const at::Scalar & threshold);
static at::Tensor trace(const at::Tensor & self);
static at::Tensor transpose_copy(const at::Tensor & self, int64_t dim0, int64_t dim1);
static at::Tensor tril(const at::Tensor & self, int64_t diagonal);
static at::Tensor triu(const at::Tensor & self, int64_t diagonal);
static at::Tensor trunc(const at::Tensor & self);
static at::Tensor unfold_copy(const at::Tensor & self, int64_t dimension, int64_t size, int64_t step);
static at::Tensor uniform(const at::Tensor & self, double from, double to, ::std::optional<at::Generator> generator);
static at::Tensor unsqueeze_copy(const at::Tensor & self, int64_t dim);
static at::Tensor upsample_bilinear2d(const at::Tensor & self, at::IntArrayRef output_size, bool align_corners, ::std::optional<double> scales_h, ::std::optional<double> scales_w);
static at::Tensor upsample_bilinear2d_backward(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, bool align_corners, ::std::optional<double> scales_h, ::std::optional<double> scales_w);
static at::Tensor upsample_nearest2d(const at::Tensor & self, at::IntArrayRef output_size, ::std::optional<double> scales_h, ::std::optional<double> scales_w);
static at::Tensor upsample_nearest2d_backward(const at::Tensor & grad_output, at::IntArrayRef output_size, at::IntArrayRef input_size, ::std::optional<double> scales_h, ::std::optional<double> scales_w);
static at::Tensor view_copy(const at::Tensor & self, at::ScalarType dtype);
static at::Tensor view_copy_symint(const at::Tensor & self, c10::SymIntArrayRef size);
static at::Tensor zero(const at::Tensor & self);
static ::std::tuple<at::Tensor,at::Tensor,at::Tensor> native_group_norm(const at::Tensor & input, const ::std::optional<at::Tensor> & weight, const ::std::optional<at::Tensor> & bias, int64_t N, int64_t C, int64_t HxW, int64_t group, double eps);
static at::Tensor max_pool3d(const at::Tensor & self, at::IntArrayRef kernel_size, at::IntArrayRef stride, at::IntArrayRef padding, at::IntArrayRef dilation, bool ceil_mode);

};
} // namespace lazy
} // namespace torch
