# This module is autogenerated by build_short_name_to_treebank.py
# Please do not edit

SHORT_NAMES = {
    'abq_atb':                   'UD_Abaza-ATB',
    'ab_abnc':                   'UD_Abkhaz-AbNC',
    'af_afribooms':              'UD_Afrikaans-AfriBooms',
    'akk_pisandub':              'UD_Akkadian-PISANDUB',
    'akk_riao':                  'UD_Akkadian-RIAO',
    'aqz_tudet':                 'UD_Akuntsu-TuDeT',
    'sq_staf':                   'UD_Albanian-STAF',
    'sq_tsa':                    'UD_Albanian-TSA',
    'am_att':                    'UD_Amharic-ATT',
    'grc_proiel':                'UD_Ancient_Greek-PROIEL',
    'grc_ptnk':                  'UD_Ancient_Greek-PTNK',
    'grc_perseus':               'UD_Ancient_Greek-Perseus',
    'hbo_ptnk':                  'UD_Ancient_Hebrew-PTNK',
    'apu_ufpa':                  'UD_Apurina-UFPA',
    'ar_nyuad':                  'UD_Arabic-NYUAD',
    'ar_padt':                   'UD_Arabic-PADT',
    'ar_pud':                    'UD_Arabic-PUD',
    'hy_armtdp':                 'UD_Armenian-ArmTDP',
    'hy_bsut':                   'UD_Armenian-BSUT',
    'aii_as':                    'UD_Assyrian-AS',
    'az_tuecl':                  'UD_Azerbaijani-TueCL',
    'bm_crb':                    'UD_Bambara-CRB',
    'eu_bdt':                    'UD_Basque-BDT',
    'bar_maibaam':               'UD_Bavarian-MaiBaam',
    'bej_autogramm':             'UD_Beja-Autogramm',
    'be_hse':                    'UD_Belarusian-HSE',
    'bn_bru':                    'UD_Bengali-BRU',
    'bho_bhtb':                  'UD_Bhojpuri-BHTB',
    'bor_bdt':                   'UD_Bororo-BDT',
    'br_keb':                    'UD_Breton-KEB',
    'bg_btb':                    'UD_Bulgarian-BTB',
    'bxr_bdt':                   'UD_Buryat-BDT',
    'yue_hk':                    'UD_Cantonese-HK',
    'cpg_amgic':                 'UD_Cappadocian-AMGiC',
    'cpg_tuecl':                 'UD_Cappadocian-TueCL',
    'ca_ancora':                 'UD_Catalan-AnCora',
    'ceb_gja':                   'UD_Cebuano-GJA',
    'zh-hans_beginner':          'UD_Chinese-Beginner',
    'zh_beginner':               'UD_Chinese-Beginner',
    'zh-hans_cfl':               'UD_Chinese-CFL',
    'zh_cfl':                    'UD_Chinese-CFL',
    'zh-hant_gsd':               'UD_Chinese-GSD',
    'zh_gsd':                    'UD_Chinese-GSD',
    'zh-hans_gsdsimp':           'UD_Chinese-GSDSimp',
    'zh_gsdsimp':                'UD_Chinese-GSDSimp',
    'zh-hant_hk':                'UD_Chinese-HK',
    'zh_hk':                     'UD_Chinese-HK',
    'zh-hant_pud':               'UD_Chinese-PUD',
    'zh_pud':                    'UD_Chinese-PUD',
    'zh-hans_patentchar':        'UD_Chinese-PatentChar',
    'zh_patentchar':             'UD_Chinese-PatentChar',
    'ckt_hse':                   'UD_Chukchi-HSE',
    'xcl_caval':                 'UD_Classical_Armenian-CAVaL',
    'lzh_kyoto':                 'UD_Classical_Chinese-Kyoto',
    'lzh_tuecl':                 'UD_Classical_Chinese-TueCL',
    'cop_scriptorium':           'UD_Coptic-Scriptorium',
    'hr_set':                    'UD_Croatian-SET',
    'cs_cac':                    'UD_Czech-CAC',
    'cs_cltt':                   'UD_Czech-CLTT',
    'cs_fictree':                'UD_Czech-FicTree',
    'cs_pdt':                    'UD_Czech-PDT',
    'cs_pud':                    'UD_Czech-PUD',
    'cs_poetry':                 'UD_Czech-Poetry',
    'da_ddt':                    'UD_Danish-DDT',
    'nl_alpino':                 'UD_Dutch-Alpino',
    'nl_lassysmall':             'UD_Dutch-LassySmall',
    'egy_ujaen':                 'UD_Egyptian-UJaen',
    'en_atis':                   'UD_English-Atis',
    'en_ctetex':                 'UD_English-CTeTex',
    'en_eslspok':                'UD_English-ESLSpok',
    'en_ewt':                    'UD_English-EWT',
    'en_gentle':                 'UD_English-GENTLE',
    'en_gum':                    'UD_English-GUM',
    'en_gumreddit':              'UD_English-GUMReddit',
    'en_lines':                  'UD_English-LinES',
    'en_pud':                    'UD_English-PUD',
    'en_partut':                 'UD_English-ParTUT',
    'en_pronouns':               'UD_English-Pronouns',
    'myv_jr':                    'UD_Erzya-JR',
    'et_edt':                    'UD_Estonian-EDT',
    'et_ewt':                    'UD_Estonian-EWT',
    'fo_farpahc':                'UD_Faroese-FarPaHC',
    'fo_oft':                    'UD_Faroese-OFT',
    'fi_ftb':                    'UD_Finnish-FTB',
    'fi_ood':                    'UD_Finnish-OOD',
    'fi_pud':                    'UD_Finnish-PUD',
    'fi_tdt':                    'UD_Finnish-TDT',
    'fr_fqb':                    'UD_French-FQB',
    'fr_gsd':                    'UD_French-GSD',
    'fr_pud':                    'UD_French-PUD',
    'fr_partut':                 'UD_French-ParTUT',
    'fr_parisstories':           'UD_French-ParisStories',
    'fr_rhapsodie':              'UD_French-Rhapsodie',
    'fr_sequoia':                'UD_French-Sequoia',
    'qfn_fame':                  'UD_Frisian_Dutch-Fame',
    'gl_ctg':                    'UD_Galician-CTG',
    'gl_pud':                    'UD_Galician-PUD',
    'gl_treegal':                'UD_Galician-TreeGal',
    'ka_glc':                    'UD_Georgian-GLC',
    'de_gsd':                    'UD_German-GSD',
    'de_hdt':                    'UD_German-HDT',
    'de_lit':                    'UD_German-LIT',
    'de_pud':                    'UD_German-PUD',
    'aln_gps':                   'UD_Gheg-GPS',
    'got_proiel':                'UD_Gothic-PROIEL',
    'el_gdt':                    'UD_Greek-GDT',
    'el_gud':                    'UD_Greek-GUD',
    'gub_tudet':                 'UD_Guajajara-TuDeT',
    'gn_oldtudet':               'UD_Guarani-OldTuDeT',
    'gu_gujtb':                  'UD_Gujarati-GujTB',
    'gwi_tuecl':                 'UD_Gwichin-TueCL',
    'ht_autogramm':              'UD_Haitian_Creole-Autogramm',
    'ha_northernautogramm':      'UD_Hausa-NorthernAutogramm',
    'ha_southernautogramm':      'UD_Hausa-SouthernAutogramm',
    'he_htb':                    'UD_Hebrew-HTB',
    'he_iahltknesset':           'UD_Hebrew-IAHLTknesset',
    'he_iahltwiki':              'UD_Hebrew-IAHLTwiki',
    'azz_itml':                  'UD_Highland_Puebla_Nahuatl-ITML',
    'hi_hdtb':                   'UD_Hindi-HDTB',
    'hi_pud':                    'UD_Hindi-PUD',
    'hit_hittb':                 'UD_Hittite-HitTB',
    'hu_szeged':                 'UD_Hungarian-Szeged',
    'is_gc':                     'UD_Icelandic-GC',
    'is_icepahc':                'UD_Icelandic-IcePaHC',
    'is_modern':                 'UD_Icelandic-Modern',
    'is_pud':                    'UD_Icelandic-PUD',
    'id_csui':                   'UD_Indonesian-CSUI',
    'id_gsd':                    'UD_Indonesian-GSD',
    'id_pud':                    'UD_Indonesian-PUD',
    'ga_cadhan':                 'UD_Irish-Cadhan',
    'ga_idt':                    'UD_Irish-IDT',
    'ga_twittirish':             'UD_Irish-TwittIrish',
    'it_isdt':                   'UD_Italian-ISDT',
    'it_markit':                 'UD_Italian-MarkIT',
    'it_old':                    'UD_Italian-Old',
    'it_pud':                    'UD_Italian-PUD',
    'it_partut':                 'UD_Italian-ParTUT',
    'it_parlamint':              'UD_Italian-ParlaMint',
    'it_postwita':               'UD_Italian-PoSTWITA',
    'it_twittiro':               'UD_Italian-TWITTIRO',
    'it_vit':                    'UD_Italian-VIT',
    'it_valico':                 'UD_Italian-Valico',
    'ja_bccwj':                  'UD_Japanese-BCCWJ',
    'ja_bccwjluw':               'UD_Japanese-BCCWJLUW',
    'ja_gsd':                    'UD_Japanese-GSD',
    'ja_gsdluw':                 'UD_Japanese-GSDLUW',
    'ja_pud':                    'UD_Japanese-PUD',
    'ja_pudluw':                 'UD_Japanese-PUDLUW',
    'jv_csui':                   'UD_Javanese-CSUI',
    'urb_tudet':                 'UD_Kaapor-TuDeT',
    'xnr_kdtb':                  'UD_Kangri-KDTB',
    'krl_kkpp':                  'UD_Karelian-KKPP',
    'arr_tudet':                 'UD_Karo-TuDeT',
    'kk_ktb':                    'UD_Kazakh-KTB',
    'kfm_aha':                   'UD_Khunsari-AHA',
    'quc_iu':                    'UD_Kiche-IU',
    'koi_uh':                    'UD_Komi_Permyak-UH',
    'kpv_ikdp':                  'UD_Komi_Zyrian-IKDP',
    'kpv_lattice':               'UD_Komi_Zyrian-Lattice',
    'ko_gsd':                    'UD_Korean-GSD',
    'ko_ksl':                    'UD_Korean-KSL',
    'ko_kaist':                  'UD_Korean-Kaist',
    'ko_pud':                    'UD_Korean-PUD',
    'kmr_mg':                    'UD_Kurmanji-MG',
    'ky_ktmu':                   'UD_Kyrgyz-KTMU',
    'ky_tuecl':                  'UD_Kyrgyz-TueCL',
    'ltg_cairo':                 'UD_Latgalian-Cairo',
    'la_circse':                 'UD_Latin-CIRCSE',
    'la_ittb':                   'UD_Latin-ITTB',
    'la_llct':                   'UD_Latin-LLCT',
    'la_proiel':                 'UD_Latin-PROIEL',
    'la_perseus':                'UD_Latin-Perseus',
    'la_udante':                 'UD_Latin-UDante',
    'lv_cairo':                  'UD_Latvian-Cairo',
    'lv_lvtb':                   'UD_Latvian-LVTB',
    'lij_glt':                   'UD_Ligurian-GLT',
    'lt_alksnis':                'UD_Lithuanian-ALKSNIS',
    'lt_hse':                    'UD_Lithuanian-HSE',
    'olo_kkpp':                  'UD_Livvi-KKPP',
    'nds_lsdc':                  'UD_Low_Saxon-LSDC',
    'lb_luxbank':                'UD_Luxembourgish-LuxBank',
    'mk_mtb':                    'UD_Macedonian-MTB',
    'jaa_jarawara':              'UD_Madi-Jarawara',
    'qaf_arabizi':               'UD_Maghrebi_Arabic_French-Arabizi',
    'mpu_tudet':                 'UD_Makurap-TuDeT',
    'ml_ufal':                   'UD_Malayalam-UFAL',
    'mt_mudt':                   'UD_Maltese-MUDT',
    'gv_cadhan':                 'UD_Manx-Cadhan',
    'mr_ufal':                   'UD_Marathi-UFAL',
    'gun_dooley':                'UD_Mbya_Guarani-Dooley',
    'gun_thomas':                'UD_Mbya_Guarani-Thomas',
    'frm_profiterole':           'UD_Middle_French-PROFITEROLE',
    'mdf_jr':                    'UD_Moksha-JR',
    'myu_tudet':                 'UD_Munduruku-TuDeT',
    'pcm_nsc':                   'UD_Naija-NSC',
    'nyq_aha':                   'UD_Nayini-AHA',
    'nap_rb':                    'UD_Neapolitan-RB',
    'yrl_complin':               'UD_Nheengatu-CompLin',
    'sme_giella':                'UD_North_Sami-Giella',
    'gya_autogramm':             'UD_Northwest_Gbaya-Autogramm',
    'nb_bokmaal':                'UD_Norwegian-Bokmaal',
    'no_bokmaal':                'UD_Norwegian-Bokmaal',
    'nn_nynorsk':                'UD_Norwegian-Nynorsk',
    'cu_proiel':                 'UD_Old_Church_Slavonic-PROIEL',
    'orv_birchbark':             'UD_Old_East_Slavic-Birchbark',
    'orv_rnc':                   'UD_Old_East_Slavic-RNC',
    'orv_ruthenian':             'UD_Old_East_Slavic-Ruthenian',
    'orv_torot':                 'UD_Old_East_Slavic-TOROT',
    'fro_profiterole':           'UD_Old_French-PROFITEROLE',
    'sga_dipsgg':                'UD_Old_Irish-DipSGG',
    'sga_dipwbg':                'UD_Old_Irish-DipWBG',
    'otk_clausal':               'UD_Old_Turkish-Clausal',
    'ota_boun':                  'UD_Ottoman_Turkish-BOUN',
    'ota_dudu':                  'UD_Ottoman_Turkish-DUDU',
    'ps_sikaram':                'UD_Pashto-Sikaram',
    'pad_tuecl':                 'UD_Paumari-TueCL',
    'fa_perdt':                  'UD_Persian-PerDT',
    'fa_seraji':                 'UD_Persian-Seraji',
    'pay_chibergis':             'UD_Pesh-ChibErgIS',
    'xpg_kul':                   'UD_Phrygian-KUL',
    'pl_lfg':                    'UD_Polish-LFG',
    'pl_pdb':                    'UD_Polish-PDB',
    'pl_pud':                    'UD_Polish-PUD',
    'qpm_philotis':              'UD_Pomak-Philotis',
    'pt_bosque':                 'UD_Portuguese-Bosque',
    'pt_cintil':                 'UD_Portuguese-CINTIL',
    'pt_dantestocks':            'UD_Portuguese-DANTEStocks',
    'pt_gsd':                    'UD_Portuguese-GSD',
    'pt_pud':                    'UD_Portuguese-PUD',
    'pt_petrogold':              'UD_Portuguese-PetroGold',
    'pt_porttinari':             'UD_Portuguese-Porttinari',
    'ro_art':                    'UD_Romanian-ArT',
    'ro_nonstandard':            'UD_Romanian-Nonstandard',
    'ro_rrt':                    'UD_Romanian-RRT',
    'ro_simonero':               'UD_Romanian-SiMoNERo',
    'ro_tuecl':                  'UD_Romanian-TueCL',
    'ru_gsd':                    'UD_Russian-GSD',
    'ru_pud':                    'UD_Russian-PUD',
    'ru_poetry':                 'UD_Russian-Poetry',
    'ru_syntagrus':              'UD_Russian-SynTagRus',
    'ru_taiga':                  'UD_Russian-Taiga',
    'sa_ufal':                   'UD_Sanskrit-UFAL',
    'sa_vedic':                  'UD_Sanskrit-Vedic',
    'gd_arcosg':                 'UD_Scottish_Gaelic-ARCOSG',
    'sr_set':                    'UD_Serbian-SET',
    'si_stb':                    'UD_Sinhala-STB',
    'sms_giellagas':             'UD_Skolt_Sami-Giellagas',
    'sk_snk':                    'UD_Slovak-SNK',
    'sl_ssj':                    'UD_Slovenian-SSJ',
    'sl_sst':                    'UD_Slovenian-SST',
    'soj_aha':                   'UD_Soi-AHA',
    'ajp_madar':                 'UD_South_Levantine_Arabic-MADAR',
    'es_ancora':                 'UD_Spanish-AnCora',
    'es_coser':                  'UD_Spanish-COSER',
    'es_gsd':                    'UD_Spanish-GSD',
    'es_pud':                    'UD_Spanish-PUD',
    'ssp_lse':                   'UD_Spanish_Sign_Language-LSE',
    'sv_lines':                  'UD_Swedish-LinES',
    'sv_pud':                    'UD_Swedish-PUD',
    'sv_talbanken':              'UD_Swedish-Talbanken',
    'swl_sslc':                  'UD_Swedish_Sign_Language-SSLC',
    'gsw_uzh':                   'UD_Swiss_German-UZH',
    'tl_trg':                    'UD_Tagalog-TRG',
    'tl_ugnayan':                'UD_Tagalog-Ugnayan',
    'ta_mwtt':                   'UD_Tamil-MWTT',
    'ta_ttb':                    'UD_Tamil-TTB',
    'tt_nmctt':                  'UD_Tatar-NMCTT',
    'eme_tudet':                 'UD_Teko-TuDeT',
    'te_mtg':                    'UD_Telugu-MTG',
    'qte_tect':                  'UD_Telugu_English-TECT',
    'th_pud':                    'UD_Thai-PUD',
    'tn_popapolelo':             'UD_Tswana-Popapolelo',
    'tpn_tudet':                 'UD_Tupinamba-TuDeT',
    'tr_atis':                   'UD_Turkish-Atis',
    'tr_boun':                   'UD_Turkish-BOUN',
    'tr_framenet':               'UD_Turkish-FrameNet',
    'tr_gb':                     'UD_Turkish-GB',
    'tr_imst':                   'UD_Turkish-IMST',
    'tr_kenet':                  'UD_Turkish-Kenet',
    'tr_pud':                    'UD_Turkish-PUD',
    'tr_penn':                   'UD_Turkish-Penn',
    'tr_tourism':                'UD_Turkish-Tourism',
    'qtd_sagt':                  'UD_Turkish_German-SAGT',
    'uk_iu':                     'UD_Ukrainian-IU',
    'uk_parlamint':              'UD_Ukrainian-ParlaMint',
    'xum_ikuvina':               'UD_Umbrian-IKUVINA',
    'hsb_ufal':                  'UD_Upper_Sorbian-UFAL',
    'ur_udtb':                   'UD_Urdu-UDTB',
    'ug_udt':                    'UD_Uyghur-UDT',
    'uz_ut':                     'UD_Uzbek-UT',
    'vep_vwt':                   'UD_Veps-VWT',
    'vi_tuecl':                  'UD_Vietnamese-TueCL',
    'vi_vtb':                    'UD_Vietnamese-VTB',
    'wbp_ufal':                  'UD_Warlpiri-UFAL',
    'cy_ccg':                    'UD_Welsh-CCG',
    'hyw_armtdp':                'UD_Western_Armenian-ArmTDP',
    'nhi_itml':                  'UD_Western_Sierra_Puebla_Nahuatl-ITML',
    'wo_wtb':                    'UD_Wolof-WTB',
    'xav_xdt':                   'UD_Xavante-XDT',
    'sjo_xdt':                   'UD_Xibe-XDT',
    'sah_yktdt':                 'UD_Yakut-YKTDT',
    'yo_ytb':                    'UD_Yoruba-YTB',
    'ess_sli':                   'UD_Yupik-SLI',
    'say_autogramm':             'UD_Zaar-Autogramm',
}


def short_name_to_treebank(short_name):
    return SHORT_NAMES[short_name]


CANONICAL_NAMES = {
    'ud_abaza-atb':                            'UD_Abaza-ATB',
    'ud_abkhaz-abnc':                          'UD_Abkhaz-AbNC',
    'ud_afrikaans-afribooms':                  'UD_Afrikaans-AfriBooms',
    'ud_akkadian-pisandub':                    'UD_Akkadian-PISANDUB',
    'ud_akkadian-riao':                        'UD_Akkadian-RIAO',
    'ud_akuntsu-tudet':                        'UD_Akuntsu-TuDeT',
    'ud_albanian-staf':                        'UD_Albanian-STAF',
    'ud_albanian-tsa':                         'UD_Albanian-TSA',
    'ud_amharic-att':                          'UD_Amharic-ATT',
    'ud_ancient_greek-proiel':                 'UD_Ancient_Greek-PROIEL',
    'ud_ancient_greek-ptnk':                   'UD_Ancient_Greek-PTNK',
    'ud_ancient_greek-perseus':                'UD_Ancient_Greek-Perseus',
    'ud_ancient_hebrew-ptnk':                  'UD_Ancient_Hebrew-PTNK',
    'ud_apurina-ufpa':                         'UD_Apurina-UFPA',
    'ud_arabic-nyuad':                         'UD_Arabic-NYUAD',
    'ud_arabic-padt':                          'UD_Arabic-PADT',
    'ud_arabic-pud':                           'UD_Arabic-PUD',
    'ud_armenian-armtdp':                      'UD_Armenian-ArmTDP',
    'ud_armenian-bsut':                        'UD_Armenian-BSUT',
    'ud_assyrian-as':                          'UD_Assyrian-AS',
    'ud_azerbaijani-tuecl':                    'UD_Azerbaijani-TueCL',
    'ud_bambara-crb':                          'UD_Bambara-CRB',
    'ud_basque-bdt':                           'UD_Basque-BDT',
    'ud_bavarian-maibaam':                     'UD_Bavarian-MaiBaam',
    'ud_beja-autogramm':                       'UD_Beja-Autogramm',
    'ud_belarusian-hse':                       'UD_Belarusian-HSE',
    'ud_bengali-bru':                          'UD_Bengali-BRU',
    'ud_bhojpuri-bhtb':                        'UD_Bhojpuri-BHTB',
    'ud_bororo-bdt':                           'UD_Bororo-BDT',
    'ud_breton-keb':                           'UD_Breton-KEB',
    'ud_bulgarian-btb':                        'UD_Bulgarian-BTB',
    'ud_buryat-bdt':                           'UD_Buryat-BDT',
    'ud_cantonese-hk':                         'UD_Cantonese-HK',
    'ud_cappadocian-amgic':                    'UD_Cappadocian-AMGiC',
    'ud_cappadocian-tuecl':                    'UD_Cappadocian-TueCL',
    'ud_catalan-ancora':                       'UD_Catalan-AnCora',
    'ud_cebuano-gja':                          'UD_Cebuano-GJA',
    'ud_chinese-beginner':                     'UD_Chinese-Beginner',
    'ud_chinese-cfl':                          'UD_Chinese-CFL',
    'ud_chinese-gsd':                          'UD_Chinese-GSD',
    'ud_chinese-gsdsimp':                      'UD_Chinese-GSDSimp',
    'ud_chinese-hk':                           'UD_Chinese-HK',
    'ud_chinese-pud':                          'UD_Chinese-PUD',
    'ud_chinese-patentchar':                   'UD_Chinese-PatentChar',
    'ud_chukchi-hse':                          'UD_Chukchi-HSE',
    'ud_classical_armenian-caval':             'UD_Classical_Armenian-CAVaL',
    'ud_classical_chinese-kyoto':              'UD_Classical_Chinese-Kyoto',
    'ud_classical_chinese-tuecl':              'UD_Classical_Chinese-TueCL',
    'ud_coptic-scriptorium':                   'UD_Coptic-Scriptorium',
    'ud_croatian-set':                         'UD_Croatian-SET',
    'ud_czech-cac':                            'UD_Czech-CAC',
    'ud_czech-cltt':                           'UD_Czech-CLTT',
    'ud_czech-fictree':                        'UD_Czech-FicTree',
    'ud_czech-pdt':                            'UD_Czech-PDT',
    'ud_czech-pud':                            'UD_Czech-PUD',
    'ud_czech-poetry':                         'UD_Czech-Poetry',
    'ud_danish-ddt':                           'UD_Danish-DDT',
    'ud_dutch-alpino':                         'UD_Dutch-Alpino',
    'ud_dutch-lassysmall':                     'UD_Dutch-LassySmall',
    'ud_egyptian-ujaen':                       'UD_Egyptian-UJaen',
    'ud_english-atis':                         'UD_English-Atis',
    'ud_english-ctetex':                       'UD_English-CTeTex',
    'ud_english-eslspok':                      'UD_English-ESLSpok',
    'ud_english-ewt':                          'UD_English-EWT',
    'ud_english-gentle':                       'UD_English-GENTLE',
    'ud_english-gum':                          'UD_English-GUM',
    'ud_english-gumreddit':                    'UD_English-GUMReddit',
    'ud_english-lines':                        'UD_English-LinES',
    'ud_english-pud':                          'UD_English-PUD',
    'ud_english-partut':                       'UD_English-ParTUT',
    'ud_english-pronouns':                     'UD_English-Pronouns',
    'ud_erzya-jr':                             'UD_Erzya-JR',
    'ud_estonian-edt':                         'UD_Estonian-EDT',
    'ud_estonian-ewt':                         'UD_Estonian-EWT',
    'ud_faroese-farpahc':                      'UD_Faroese-FarPaHC',
    'ud_faroese-oft':                          'UD_Faroese-OFT',
    'ud_finnish-ftb':                          'UD_Finnish-FTB',
    'ud_finnish-ood':                          'UD_Finnish-OOD',
    'ud_finnish-pud':                          'UD_Finnish-PUD',
    'ud_finnish-tdt':                          'UD_Finnish-TDT',
    'ud_french-fqb':                           'UD_French-FQB',
    'ud_french-gsd':                           'UD_French-GSD',
    'ud_french-pud':                           'UD_French-PUD',
    'ud_french-partut':                        'UD_French-ParTUT',
    'ud_french-parisstories':                  'UD_French-ParisStories',
    'ud_french-rhapsodie':                     'UD_French-Rhapsodie',
    'ud_french-sequoia':                       'UD_French-Sequoia',
    'ud_frisian_dutch-fame':                   'UD_Frisian_Dutch-Fame',
    'ud_galician-ctg':                         'UD_Galician-CTG',
    'ud_galician-pud':                         'UD_Galician-PUD',
    'ud_galician-treegal':                     'UD_Galician-TreeGal',
    'ud_georgian-glc':                         'UD_Georgian-GLC',
    'ud_german-gsd':                           'UD_German-GSD',
    'ud_german-hdt':                           'UD_German-HDT',
    'ud_german-lit':                           'UD_German-LIT',
    'ud_german-pud':                           'UD_German-PUD',
    'ud_gheg-gps':                             'UD_Gheg-GPS',
    'ud_gothic-proiel':                        'UD_Gothic-PROIEL',
    'ud_greek-gdt':                            'UD_Greek-GDT',
    'ud_greek-gud':                            'UD_Greek-GUD',
    'ud_guajajara-tudet':                      'UD_Guajajara-TuDeT',
    'ud_guarani-oldtudet':                     'UD_Guarani-OldTuDeT',
    'ud_gujarati-gujtb':                       'UD_Gujarati-GujTB',
    'ud_gwichin-tuecl':                        'UD_Gwichin-TueCL',
    'ud_haitian_creole-autogramm':             'UD_Haitian_Creole-Autogramm',
    'ud_hausa-northernautogramm':              'UD_Hausa-NorthernAutogramm',
    'ud_hausa-southernautogramm':              'UD_Hausa-SouthernAutogramm',
    'ud_hebrew-htb':                           'UD_Hebrew-HTB',
    'ud_hebrew-iahltknesset':                  'UD_Hebrew-IAHLTknesset',
    'ud_hebrew-iahltwiki':                     'UD_Hebrew-IAHLTwiki',
    'ud_highland_puebla_nahuatl-itml':         'UD_Highland_Puebla_Nahuatl-ITML',
    'ud_hindi-hdtb':                           'UD_Hindi-HDTB',
    'ud_hindi-pud':                            'UD_Hindi-PUD',
    'ud_hittite-hittb':                        'UD_Hittite-HitTB',
    'ud_hungarian-szeged':                     'UD_Hungarian-Szeged',
    'ud_icelandic-gc':                         'UD_Icelandic-GC',
    'ud_icelandic-icepahc':                    'UD_Icelandic-IcePaHC',
    'ud_icelandic-modern':                     'UD_Icelandic-Modern',
    'ud_icelandic-pud':                        'UD_Icelandic-PUD',
    'ud_indonesian-csui':                      'UD_Indonesian-CSUI',
    'ud_indonesian-gsd':                       'UD_Indonesian-GSD',
    'ud_indonesian-pud':                       'UD_Indonesian-PUD',
    'ud_irish-cadhan':                         'UD_Irish-Cadhan',
    'ud_irish-idt':                            'UD_Irish-IDT',
    'ud_irish-twittirish':                     'UD_Irish-TwittIrish',
    'ud_italian-isdt':                         'UD_Italian-ISDT',
    'ud_italian-markit':                       'UD_Italian-MarkIT',
    'ud_italian-old':                          'UD_Italian-Old',
    'ud_italian-pud':                          'UD_Italian-PUD',
    'ud_italian-partut':                       'UD_Italian-ParTUT',
    'ud_italian-parlamint':                    'UD_Italian-ParlaMint',
    'ud_italian-postwita':                     'UD_Italian-PoSTWITA',
    'ud_italian-twittiro':                     'UD_Italian-TWITTIRO',
    'ud_italian-vit':                          'UD_Italian-VIT',
    'ud_italian-valico':                       'UD_Italian-Valico',
    'ud_japanese-bccwj':                       'UD_Japanese-BCCWJ',
    'ud_japanese-bccwjluw':                    'UD_Japanese-BCCWJLUW',
    'ud_japanese-gsd':                         'UD_Japanese-GSD',
    'ud_japanese-gsdluw':                      'UD_Japanese-GSDLUW',
    'ud_japanese-pud':                         'UD_Japanese-PUD',
    'ud_japanese-pudluw':                      'UD_Japanese-PUDLUW',
    'ud_javanese-csui':                        'UD_Javanese-CSUI',
    'ud_kaapor-tudet':                         'UD_Kaapor-TuDeT',
    'ud_kangri-kdtb':                          'UD_Kangri-KDTB',
    'ud_karelian-kkpp':                        'UD_Karelian-KKPP',
    'ud_karo-tudet':                           'UD_Karo-TuDeT',
    'ud_kazakh-ktb':                           'UD_Kazakh-KTB',
    'ud_khunsari-aha':                         'UD_Khunsari-AHA',
    'ud_kiche-iu':                             'UD_Kiche-IU',
    'ud_komi_permyak-uh':                      'UD_Komi_Permyak-UH',
    'ud_komi_zyrian-ikdp':                     'UD_Komi_Zyrian-IKDP',
    'ud_komi_zyrian-lattice':                  'UD_Komi_Zyrian-Lattice',
    'ud_korean-gsd':                           'UD_Korean-GSD',
    'ud_korean-ksl':                           'UD_Korean-KSL',
    'ud_korean-kaist':                         'UD_Korean-Kaist',
    'ud_korean-pud':                           'UD_Korean-PUD',
    'ud_kurmanji-mg':                          'UD_Kurmanji-MG',
    'ud_kyrgyz-ktmu':                          'UD_Kyrgyz-KTMU',
    'ud_kyrgyz-tuecl':                         'UD_Kyrgyz-TueCL',
    'ud_latgalian-cairo':                      'UD_Latgalian-Cairo',
    'ud_latin-circse':                         'UD_Latin-CIRCSE',
    'ud_latin-ittb':                           'UD_Latin-ITTB',
    'ud_latin-llct':                           'UD_Latin-LLCT',
    'ud_latin-proiel':                         'UD_Latin-PROIEL',
    'ud_latin-perseus':                        'UD_Latin-Perseus',
    'ud_latin-udante':                         'UD_Latin-UDante',
    'ud_latvian-cairo':                        'UD_Latvian-Cairo',
    'ud_latvian-lvtb':                         'UD_Latvian-LVTB',
    'ud_ligurian-glt':                         'UD_Ligurian-GLT',
    'ud_lithuanian-alksnis':                   'UD_Lithuanian-ALKSNIS',
    'ud_lithuanian-hse':                       'UD_Lithuanian-HSE',
    'ud_livvi-kkpp':                           'UD_Livvi-KKPP',
    'ud_low_saxon-lsdc':                       'UD_Low_Saxon-LSDC',
    'ud_luxembourgish-luxbank':                'UD_Luxembourgish-LuxBank',
    'ud_macedonian-mtb':                       'UD_Macedonian-MTB',
    'ud_madi-jarawara':                        'UD_Madi-Jarawara',
    'ud_maghrebi_arabic_french-arabizi':       'UD_Maghrebi_Arabic_French-Arabizi',
    'ud_makurap-tudet':                        'UD_Makurap-TuDeT',
    'ud_malayalam-ufal':                       'UD_Malayalam-UFAL',
    'ud_maltese-mudt':                         'UD_Maltese-MUDT',
    'ud_manx-cadhan':                          'UD_Manx-Cadhan',
    'ud_marathi-ufal':                         'UD_Marathi-UFAL',
    'ud_mbya_guarani-dooley':                  'UD_Mbya_Guarani-Dooley',
    'ud_mbya_guarani-thomas':                  'UD_Mbya_Guarani-Thomas',
    'ud_middle_french-profiterole':            'UD_Middle_French-PROFITEROLE',
    'ud_moksha-jr':                            'UD_Moksha-JR',
    'ud_munduruku-tudet':                      'UD_Munduruku-TuDeT',
    'ud_naija-nsc':                            'UD_Naija-NSC',
    'ud_nayini-aha':                           'UD_Nayini-AHA',
    'ud_neapolitan-rb':                        'UD_Neapolitan-RB',
    'ud_nheengatu-complin':                    'UD_Nheengatu-CompLin',
    'ud_north_sami-giella':                    'UD_North_Sami-Giella',
    'ud_northwest_gbaya-autogramm':            'UD_Northwest_Gbaya-Autogramm',
    'ud_norwegian-bokmaal':                    'UD_Norwegian-Bokmaal',
    'ud_norwegian-nynorsk':                    'UD_Norwegian-Nynorsk',
    'ud_old_church_slavonic-proiel':           'UD_Old_Church_Slavonic-PROIEL',
    'ud_old_east_slavic-birchbark':            'UD_Old_East_Slavic-Birchbark',
    'ud_old_east_slavic-rnc':                  'UD_Old_East_Slavic-RNC',
    'ud_old_east_slavic-ruthenian':            'UD_Old_East_Slavic-Ruthenian',
    'ud_old_east_slavic-torot':                'UD_Old_East_Slavic-TOROT',
    'ud_old_french-profiterole':               'UD_Old_French-PROFITEROLE',
    'ud_old_irish-dipsgg':                     'UD_Old_Irish-DipSGG',
    'ud_old_irish-dipwbg':                     'UD_Old_Irish-DipWBG',
    'ud_old_turkish-clausal':                  'UD_Old_Turkish-Clausal',
    'ud_ottoman_turkish-boun':                 'UD_Ottoman_Turkish-BOUN',
    'ud_ottoman_turkish-dudu':                 'UD_Ottoman_Turkish-DUDU',
    'ud_pashto-sikaram':                       'UD_Pashto-Sikaram',
    'ud_paumari-tuecl':                        'UD_Paumari-TueCL',
    'ud_persian-perdt':                        'UD_Persian-PerDT',
    'ud_persian-seraji':                       'UD_Persian-Seraji',
    'ud_pesh-chibergis':                       'UD_Pesh-ChibErgIS',
    'ud_phrygian-kul':                         'UD_Phrygian-KUL',
    'ud_polish-lfg':                           'UD_Polish-LFG',
    'ud_polish-pdb':                           'UD_Polish-PDB',
    'ud_polish-pud':                           'UD_Polish-PUD',
    'ud_pomak-philotis':                       'UD_Pomak-Philotis',
    'ud_portuguese-bosque':                    'UD_Portuguese-Bosque',
    'ud_portuguese-cintil':                    'UD_Portuguese-CINTIL',
    'ud_portuguese-dantestocks':               'UD_Portuguese-DANTEStocks',
    'ud_portuguese-gsd':                       'UD_Portuguese-GSD',
    'ud_portuguese-pud':                       'UD_Portuguese-PUD',
    'ud_portuguese-petrogold':                 'UD_Portuguese-PetroGold',
    'ud_portuguese-porttinari':                'UD_Portuguese-Porttinari',
    'ud_romanian-art':                         'UD_Romanian-ArT',
    'ud_romanian-nonstandard':                 'UD_Romanian-Nonstandard',
    'ud_romanian-rrt':                         'UD_Romanian-RRT',
    'ud_romanian-simonero':                    'UD_Romanian-SiMoNERo',
    'ud_romanian-tuecl':                       'UD_Romanian-TueCL',
    'ud_russian-gsd':                          'UD_Russian-GSD',
    'ud_russian-pud':                          'UD_Russian-PUD',
    'ud_russian-poetry':                       'UD_Russian-Poetry',
    'ud_russian-syntagrus':                    'UD_Russian-SynTagRus',
    'ud_russian-taiga':                        'UD_Russian-Taiga',
    'ud_sanskrit-ufal':                        'UD_Sanskrit-UFAL',
    'ud_sanskrit-vedic':                       'UD_Sanskrit-Vedic',
    'ud_scottish_gaelic-arcosg':               'UD_Scottish_Gaelic-ARCOSG',
    'ud_serbian-set':                          'UD_Serbian-SET',
    'ud_sinhala-stb':                          'UD_Sinhala-STB',
    'ud_skolt_sami-giellagas':                 'UD_Skolt_Sami-Giellagas',
    'ud_slovak-snk':                           'UD_Slovak-SNK',
    'ud_slovenian-ssj':                        'UD_Slovenian-SSJ',
    'ud_slovenian-sst':                        'UD_Slovenian-SST',
    'ud_soi-aha':                              'UD_Soi-AHA',
    'ud_south_levantine_arabic-madar':         'UD_South_Levantine_Arabic-MADAR',
    'ud_spanish-ancora':                       'UD_Spanish-AnCora',
    'ud_spanish-coser':                        'UD_Spanish-COSER',
    'ud_spanish-gsd':                          'UD_Spanish-GSD',
    'ud_spanish-pud':                          'UD_Spanish-PUD',
    'ud_spanish_sign_language-lse':            'UD_Spanish_Sign_Language-LSE',
    'ud_swedish-lines':                        'UD_Swedish-LinES',
    'ud_swedish-pud':                          'UD_Swedish-PUD',
    'ud_swedish-talbanken':                    'UD_Swedish-Talbanken',
    'ud_swedish_sign_language-sslc':           'UD_Swedish_Sign_Language-SSLC',
    'ud_swiss_german-uzh':                     'UD_Swiss_German-UZH',
    'ud_tagalog-trg':                          'UD_Tagalog-TRG',
    'ud_tagalog-ugnayan':                      'UD_Tagalog-Ugnayan',
    'ud_tamil-mwtt':                           'UD_Tamil-MWTT',
    'ud_tamil-ttb':                            'UD_Tamil-TTB',
    'ud_tatar-nmctt':                          'UD_Tatar-NMCTT',
    'ud_teko-tudet':                           'UD_Teko-TuDeT',
    'ud_telugu-mtg':                           'UD_Telugu-MTG',
    'ud_telugu_english-tect':                  'UD_Telugu_English-TECT',
    'ud_thai-pud':                             'UD_Thai-PUD',
    'ud_tswana-popapolelo':                    'UD_Tswana-Popapolelo',
    'ud_tupinamba-tudet':                      'UD_Tupinamba-TuDeT',
    'ud_turkish-atis':                         'UD_Turkish-Atis',
    'ud_turkish-boun':                         'UD_Turkish-BOUN',
    'ud_turkish-framenet':                     'UD_Turkish-FrameNet',
    'ud_turkish-gb':                           'UD_Turkish-GB',
    'ud_turkish-imst':                         'UD_Turkish-IMST',
    'ud_turkish-kenet':                        'UD_Turkish-Kenet',
    'ud_turkish-pud':                          'UD_Turkish-PUD',
    'ud_turkish-penn':                         'UD_Turkish-Penn',
    'ud_turkish-tourism':                      'UD_Turkish-Tourism',
    'ud_turkish_german-sagt':                  'UD_Turkish_German-SAGT',
    'ud_ukrainian-iu':                         'UD_Ukrainian-IU',
    'ud_ukrainian-parlamint':                  'UD_Ukrainian-ParlaMint',
    'ud_umbrian-ikuvina':                      'UD_Umbrian-IKUVINA',
    'ud_upper_sorbian-ufal':                   'UD_Upper_Sorbian-UFAL',
    'ud_urdu-udtb':                            'UD_Urdu-UDTB',
    'ud_uyghur-udt':                           'UD_Uyghur-UDT',
    'ud_uzbek-ut':                             'UD_Uzbek-UT',
    'ud_veps-vwt':                             'UD_Veps-VWT',
    'ud_vietnamese-tuecl':                     'UD_Vietnamese-TueCL',
    'ud_vietnamese-vtb':                       'UD_Vietnamese-VTB',
    'ud_warlpiri-ufal':                        'UD_Warlpiri-UFAL',
    'ud_welsh-ccg':                            'UD_Welsh-CCG',
    'ud_western_armenian-armtdp':              'UD_Western_Armenian-ArmTDP',
    'ud_western_sierra_puebla_nahuatl-itml':   'UD_Western_Sierra_Puebla_Nahuatl-ITML',
    'ud_wolof-wtb':                            'UD_Wolof-WTB',
    'ud_xavante-xdt':                          'UD_Xavante-XDT',
    'ud_xibe-xdt':                             'UD_Xibe-XDT',
    'ud_yakut-yktdt':                          'UD_Yakut-YKTDT',
    'ud_yoruba-ytb':                           'UD_Yoruba-YTB',
    'ud_yupik-sli':                            'UD_Yupik-SLI',
    'ud_zaar-autogramm':                       'UD_Zaar-Autogramm',
}


def canonical_treebank_name(ud_name):
    if ud_name in SHORT_NAMES:
        return SHORT_NAMES[ud_name]
    return CANONICAL_NAMES.get(ud_name.lower(), ud_name)
