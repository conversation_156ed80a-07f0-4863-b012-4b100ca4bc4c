from typing import Optional

import pandas as pd
import pytesseract
from pytesseract import Output
import cv2
import numpy as np


class OcrEngine:
    def __init__(self, lang="ces+eng", datapath=None):
        """
        Inicializace OCR enginu.
        :param lang: <PERSON><PERSON><PERSON> (např. 'ces', 'eng').
        :param datapath: cesta k tessdata, None = default.
        """
        self.lang = lang
        self.datapath = datapath

        # v Pythonu není explicitní Init() jako v C++ API,
        # pytesseract se nastaví přes environment proměnné
        if datapath is not None:
            pytesseract.pytesseract.tesseract_cmd = datapath

    def process_text(self, image: cv2.Mat) -> str:
        """
        Vrátí plain text z OpenCV obrazu (numpy array).
        """
        if image is None or not isinstance(image, np.ndarray):
            raise ValueError("Input must be a valid OpenCV image (numpy.ndarray).")

        text = pytesseract.image_to_string(image, lang=self.lang)
        return text

    def process_data(self, image: cv2.Mat, config=None) -> Optional[pd.DataFrame]:
        """
        Vrátí dataframe se strukturou blok/odstavec/řádek/slovo.
        """
        if config is None:
            config = (
                "--oem 3 --psm 11 "
                "--dpi 300 "
                "-c textord_detect_tables=0 "
                "-c textord_tabfind_vertical_text=0 "
                "-c textord_tablefind=0 "
                "-c textord_min_linespace=2 "
                "-c textord_find_heavy_artifacts=0 "
                "-c textord_detect_columns=true "
                "-c textord_noise_rowratio=8.0 "
                "-c textord_noise_area_fraction=0.9 "
                "-c textord_noise_sxfract=0.5 "
                "-c textord_noise_syfract=0.5 "
                "-c edges_children_count_threshold=15 "
                "-c textord_min_blobs_in_row=2 "
                "-c textord_spline_minblobs=3 "
                "-c textord_min_xheight=15 "
            )

        df = pytesseract.image_to_data(image, lang=self.lang,
                                       config=config,
                                       output_type=Output.DATAFRAME)
        return df

    def process_boxes(self, image: cv2.Mat, config=None):
        """
        Vrátí bounding boxy pro každý znak v obrázku.
        """

        if config is None:
            config = r'--oem 3 --psm 8'

        boxes = pytesseract.image_to_boxes(image, lang=self.lang,
                                          config=config,
                                          output_type=Output.DICT)
        return boxes

    def __del__(self):
        # v Pythonu není potřeba End(), ale v C++ ho zavoláš
        pass
