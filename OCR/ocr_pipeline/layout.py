import pandas as pd

def score_header_cluster(cl_df, page_width, page_height):
    score = 1.0

    # penalizace za číslice
    for txt in cl_df["text"]:
        if any(ch.isdigit() for ch in str(txt)):
            score *= 0.95

    # polohový koeficient (větší = lepší)
    cl_top = cl_df["top"].min()
    pos_coef = 1 - abs(cl_top - page_height / 2) / (page_height / 2)
    score *= pos_coef

    # coverage (šířka řádku / ší<PERSON>ka stránky) - menší coverage -> mírná penalizace
    left = cl_df["left"].min()
    right = (cl_df["left"] + cl_df["width"]).max()
    coverage = (right - left) / page_width
    if coverage < 0.4:
        score *= 0.9

    return score


def find_and_mark_headers(df, tolerance=10, top_n=3):
    """
    Najde top_n kandidátů na header, vybere nejlep<PERSON><PERSON><PERSON> podle score,
    a pro vybraného:
      - sjednotí všechny jeho tokeny pod jedním block_num (min původních block_num)
      - přečísluje par_num uvnitř tohoto nového bloku tak, aby byly od 1..N
      - přepíše bbox řádku level==2 pro tento block_num
      - odstraní všechny původní bloky (level==2) a jejich tokeny (level==5) kromě vybraného
    Vrací: (df_with_modifications, best_candidate_dict, list_of_candidates_dicts)
    """

    if df is None or df.empty:
        return df, None, []

    # 1) najdeme stránkové řádky podle level==1 (vynecháme je z clusteringu)
    page_rows = df[df["level"] == 1]
    if not page_rows.empty:
        page_row = page_rows.iloc[0]
        page_height = page_row["height"]
        page_width = page_row["width"]
        page_indices = page_rows.index
    else:
        # fallback
        page_width = df["left"].add(df["width"]).max()
        page_height = df["top"].add(df["height"]).max()
        page_indices = []

    # 2) tokeny bez page-řádků
    df_tokens = df.drop(index=page_indices)
    df_sorted = df_tokens.sort_values(by="top").copy()
    if df_sorted.empty:
        return df, None, []

    # 3) clusterování podle top s tolerancí (uchováváme originální indexy)
    indices = list(df_sorted.index)
    clusters = []
    current = [indices[0]]
    for idx in indices[1:]:
        prev_idx = current[-1]
        prev_top = df_sorted.at[prev_idx, "top"]
        cur_top = df_sorted.at[idx, "top"]
        if abs(cur_top - prev_top) <= tolerance:
            current.append(idx)
        else:
            clusters.append(current)
            current = [idx]
    clusters.append(current)

    # 4) vybereme top_n clusterů podle počtu tokenů
    clusters_sorted = sorted(clusters, key=lambda c: len(c), reverse=True)
    clusters_sorted = clusters_sorted[:min(top_n, len(clusters_sorted))]

    # 5) sestavíme kandidáty (zatím nic nezapisujeme do df)
    candidates = []
    for cluster_indices in clusters_sorted:
        cl_df = df.loc[cluster_indices].copy()  # vezmeme originální řádky
        left = cl_df["left"].min()
        top = cl_df["top"].min()
        right = (cl_df["left"] + cl_df["width"]).max()
        bottom = (cl_df["top"] + cl_df["height"]).max()
        bbox = (left, top, right - left, bottom - top)
        score = score_header_cluster(cl_df, page_width, page_height)
        candidates.append({
            "score": score,
            "df": cl_df,
            "bbox": bbox
        })

    if not candidates:
        return df, None, []

    # 6) vybereme nejlepšího podle score (větší = lepší)
    best_candidate = max(candidates, key=lambda x: x["score"])
    best_indices = best_candidate["df"].index

    # --- DŮLEŽITÉ: přečíslování par_num podle ORIGINÁLNÍCH (block_num, par_num) párů ---
    # vezmeme kopii dat pro vítěze (obsahuje původní block_num a par_num)
    best_df = df.loc[best_indices].copy()

    # vytvoříme klíč = (orig_block_num, orig_par_num)
    best_df["_orig_key"] = list(zip(best_df["block_num"].astype(object), best_df["par_num"].astype(object)))

    # seřadíme unikátní klíče podle vertikální pozice (min top) pro stabilní pořadí
    group_tops = best_df.groupby("_orig_key")["top"].min()
    group_tops = group_tops.sort_values()  # index = orig_key, hodnoty = min top

    # přiřadíme nová par čísla 1..N podle pořadí
    mapping = {orig_key: new_par for new_par, orig_key in enumerate(group_tops.index, start=1)}

    # aplikujeme nová par čísla pouze na tokeny vítěze (zapisujeme zpět do hlavního df)
    for idx, row in best_df.iterrows():
        orig_key = row["_orig_key"]
        new_par = mapping[orig_key]
        df.at[idx, "par_num"] = new_par

    # 7) sjednotíme block_num pro všechny tokeny vítěze pod nejmenší původní block_num
    orig_blocks = best_df["block_num"].unique()
    min_block_num = best_df["block_num"].min()
    df.loc[best_indices, "block_num"] = min_block_num

    # 8) odstraníme všechny původní bloky kromě toho min_block_num
    # Odstraníme jak block-level řádky (level == 2), tak všechny tokeny (level == 5)
    # patřící k původním blokům, které nebudou použity
    blocks_to_remove = [b for b in orig_blocks if b != min_block_num]
    if blocks_to_remove:
        # Odstraníme block-level řádky (level == 2)
        mask_remove_blocks = (df["level"] == 2) & (df["block_num"].isin(blocks_to_remove))
        # Odstraníme token-level řádky (level == 5) patřící k těmto blokům
        mask_remove_tokens = (df["level"] == 5) & (df["block_num"].isin(blocks_to_remove))
        # Kombinujeme masky a odstraníme všechny řádky najednou
        mask_remove_all = mask_remove_blocks | mask_remove_tokens
        df = df.drop(df[mask_remove_all].index)

    # 9) přepíšeme bbox toho jednoho bloku (level == 2)
    bbox = best_candidate["bbox"]
    mask_block = (df["level"] == 2) & (df["block_num"] == min_block_num)
    if mask_block.any():
        df.loc[mask_block, ["left", "top", "width", "height"]] = [bbox[0], bbox[1], bbox[2], bbox[3]]

    # vrátíme df a info
    return df, best_candidate, candidates
