import re
import unicodedata
import pandas as pd

from .constants import NOISE_CHARS, UNWANTED_CHARS, INVISIBLE_CHARS


def clean_basic_issues(df: pd.DataFrame) -> pd.DataFrame:
    """
    Opakovaně používaná funkce pro čištění artefaktů.
    Řeší problémy vzniklé během zpracování:
    - Vícenásobné mezery → jedna mezera
    - Samostatné <PERSON> (pokud nejsou čísla nebo písmena) → pryč
    - Pr<PERSON>z<PERSON><PERSON> (nebo jen s mezerou) → pryč
    - Reindexace DataFrame
    """
    # Sjednocení vícenásobných mezer na jednu
    df["text"] = df["text"].str.replace(r"\s+", " ", regex=True)

    # Ořez whitespace na začátku a konci
    df["text"] = df["text"].str.strip()

    # Odstranění jed<PERSON>ch textů, k<PERSON><PERSON> nejsou ani písmeno ani číslice
    # mask = df["text"].str.len() > 1
    # single_char_valid = df["text"].str.match(r"^[A-Za-z0-9ÁČĎÉĚÍŇÓŘŠŤÚŮÝŽáčďéěíňóřšťúůýž]$")
    # df = df[mask | single_char_valid].copy()

    # Odstranění prázdných řádků
    df = df[df["text"] != ""].copy()

    # Reindexace
    df = df.reset_index(drop=True)

    return df


def clean_and_normalize_initial(df: pd.DataFrame) -> pd.DataFrame:
    """
    Krok 2: Základní čištění a normalizace po OCR.
    - Unicode normalizace (NFKC)
    - Odstranění neviditelných znaků
    - Odstranění NOISE znaků (skutečný bordel)
    - Základní čištění artefaktů
    """

    print("Krok 2: Základní čištění a normalizace po OCR...")

    # Řádek se stránkou (level == 1) – víme, že je vždy max jeden
    if "level" in df.columns and (df["level"] == 1).any():
        idx = df.index[df["level"] == 1][0]
        df.at[idx, "text"] = "strana"
        df.at[idx, "conf"] = 100

    # Ostatní operace jen pro level == 5
    mask = df["level"] == 5

    df.loc[mask, "text"] = df.loc[mask, "text"].fillna("").astype(str)

    df.loc[mask, "text"] = df.loc[mask, "text"].map(
        lambda x: unicodedata.normalize("NFKC", x)
    )

    df.loc[mask, "text"] = df.loc[mask, "text"].map(
        lambda x: INVISIBLE_CHARS.sub("", x)
    )

    df.loc[mask, "text"] = df.loc[mask, "text"].str.replace("—", "-")

    df.loc[mask, "text"] = df.loc[mask, "text"].str.replace(NOISE_CHARS, " ", regex=True)

    df.loc[mask] = clean_basic_issues(df.loc[mask].copy())

    return df



def clean_after_processing(df: pd.DataFrame) -> pd.DataFrame:
    """
    Krok 11: Finální čištění po klasifikaci.
    Odstraňuje UNWANTED znaky z neklasifikovaných textů.
    """

    print("Krok 11: Finální čištění po klasifikaci...")

    # Pro texty bez klasifikace nebo s prázdnou klasifikací odstraníme UNWANTED znaky
    no_classification_mask = (df["value_class"] > 0)


    # Odstranění UNWANTED znaků pouze z neklasifikovaných textů
    df.loc[no_classification_mask, "text"] = (
        df.loc[no_classification_mask, "text"]
        .str.replace(UNWANTED_CHARS, " ", regex=True)
    )

    # Finální čištění artefaktů
    df = clean_basic_issues(df)

    return df