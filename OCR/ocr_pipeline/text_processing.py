import re
import pandas as pd
import cv2
from typing import Optional
from .constants import CURRENCY_MAP, TAX_MAP, ID_MAP, DEFAULT_GAP_THRESHOLD
from .cleaning import clean_basic_issues


def normalize_entities(df: pd.DataFrame) -> pd.DataFrame:
    """
    Krok 4: Normalizace entit - Currency/Tax/ID mapping.
    Převádí různé varianty na standardní formát (Kč→CZK, dph→DPH, ...)
    UNWANTED_CHARS zatím necháváme pro klasifikaci!
    """

    print("Krok 4: Normalizace entit - Currency/Tax/ID mapping...")

    def multi_replace(text: str, replacements: dict) -> str:
        for k, v in replacements.items():
            pattern = r'\b{}\b'.format(re.escape(k))
            text = re.sub(pattern, v, text)
        return text

    def clean_text(text: str) -> str:
        text = multi_replace(text, CURRENCY_MAP)
        text = multi_replace(text, TAX_MAP)
        text = multi_replace(text, ID_MAP)
        return text

    mask = df["level"] == 5

    df.loc[mask, "text"] = df.loc[mask, "text"].apply(clean_text)

    # Z<PERSON>ladní čištění artefaktů
    df = clean_basic_issues(df)

    return df


def should_merge_words(cur_text: str, nxt_text: str) -> bool:
    """Pomocná funkce pro rozhodnutí, zda spojit slova."""
    cur_text = cur_text.strip()
    nxt_text = nxt_text.strip()

    # 1. končí : nebo ;
    if cur_text.endswith((':', ';')):
        return False

    # 2. aktuální je číslo a následující obsahuje písmena
    if re.fullmatch(r"\d+", cur_text) and re.search(r"[A-Za-zÁ-ž]", nxt_text):
        return False

    # 3. aktuální nemá číslice a následující obsahuje číslice
    if not re.search(r"\d", cur_text) and re.search(r"\d", nxt_text):
        return False

    # 4. aktuální má číslice a následující neobsahuje číslice
    if re.search(r"\d", cur_text) and not (re.search(r"\d", nxt_text)):
        return False

    return True


def merge_words(df: pd.DataFrame, gap_threshold: float = DEFAULT_GAP_THRESHOLD) -> pd.DataFrame:
    """
    Krok 6: Spojování slov na stejném řádku podle pravidel.
    Zachovává ostatní levely (1–4, 6+).
    """

    print("Krok 6: Spojování slov na stejném řádku podle pravidel...")

    if df is None or df.empty:
        return pd.DataFrame()

    # určíme klíče pro identifikaci řádků
    row_keys = [k for k in ("page_num", "block_num", "par_num", "line_num") if k in df.columns]
    if not row_keys:
        groups = [("", df.copy())]
    else:
        groups = list(df.groupby(row_keys))

    merged_results = []

    for _, group in groups:
        if "level" in group.columns:
            words = group[group["level"] == 5].copy()
            others = group[group["level"] != 5].copy()
        else:
            words = group.copy()
            others = pd.DataFrame()

        words["text"] = words["text"].fillna("").astype(str)
        words_sorted = words.sort_values(by=["left"]).to_dict(orient="records")

        merged_words = []

        if words_sorted:
            # inicializujeme první slovo
            current = words_sorted[0].copy()
            current["text"] = str(current.get("text", ""))
            current["left"] = int(current.get("left", 0))
            current["top"] = int(current.get("top", 0))
            current["width"] = int(current.get("width", 0))
            current["height"] = int(current.get("height", 0))

            # uchováme původní bbox
            current_original_bbox = {
                "left": current["left"],
                "top": current["top"],
                "width": current["width"],
                "height": current["height"]
            }
            current_was_merged = False

            for nxt in words_sorted[1:]:
                nxt_entry = nxt.copy()
                nxt_entry["text"] = str(nxt_entry.get("text", ""))
                nxt_entry["left"] = int(nxt_entry.get("left", 0))
                nxt_entry["top"] = int(nxt_entry.get("top", 0))
                nxt_entry["width"] = int(nxt_entry.get("width", 0))
                nxt_entry["height"] = int(nxt_entry.get("height", 0))

                current_x1 = current["left"] + current["width"]
                next_x0 = nxt_entry["left"]
                gap = next_x0 - current_x1

                line_height = max(current["height"], nxt_entry["height"])
                gap_ratio = gap / line_height if line_height > 0 else float('inf')

                if gap_ratio <= gap_threshold and should_merge_words(current["text"], nxt_entry["text"]):
                    # sloučíme
                    new_left = min(current["left"], nxt_entry["left"])
                    new_top = min(current["top"], nxt_entry["top"])
                    new_right = max(current_x1, nxt_entry["left"] + nxt_entry["width"])
                    new_bottom = max(current["top"] + current["height"], nxt_entry["top"] + nxt_entry["height"])

                    current["text"] = (current["text"] + " " + nxt_entry["text"]).strip()
                    current["left"] = new_left
                    current["top"] = new_top
                    current["width"] = new_right - new_left
                    current["height"] = new_bottom - new_top
                    current["level"] = 5
                    current_was_merged = True
                else:
                    # slova se nespojí
                    if not current_was_merged:
                        current["left"] = current_original_bbox["left"]
                        current["top"] = current_original_bbox["top"]
                        current["width"] = current_original_bbox["width"]
                        current["height"] = current_original_bbox["height"]

                    current["level"] = 5
                    merged_words.append(current)

                    current = nxt_entry
                    current_original_bbox = {
                        "left": current["left"],
                        "top": current["top"],
                        "width": current["width"],
                        "height": current["height"]
                    }
                    current_was_merged = False

            # přidáme poslední
            if not current_was_merged:
                current["left"] = current_original_bbox["left"]
                current["top"] = current_original_bbox["top"]
                current["width"] = current_original_bbox["width"]
                current["height"] = current_original_bbox["height"]

            current["level"] = 5
            merged_words.append(current)

        # přidáme výsledky z této skupiny
        merged_results.extend(merged_words)
        if not others.empty:
            merged_results.extend(others.to_dict(orient="records"))

    if merged_results:
        result_df = pd.DataFrame(merged_results)
        # zachováme původní sloupce
        for col in df.columns:
            if col not in result_df.columns:
                result_df[col] = None
        return result_df
    else:
        return pd.DataFrame()


def split_by_delimiter(df: pd.DataFrame,
                       delimiters: Optional[list] = None,
                       ocr_engine=None,
                       image: cv2.Mat = None) -> pd.DataFrame:
    """
    Krok 8: Rozdělení textů podle oddělovačů.
    """
    if df is None or df.empty:
        return df

    if delimiters is None:
        delimiters = ['|', ':', '(', '/']

    # Najdeme kandidáty na dělení
    all_rows_to_split = []
    for delimiter in delimiters:
        delimiter_mask = df['text'].astype(str).str.contains(re.escape(delimiter), na=False)
        potential_rows = df[delimiter_mask].copy()
        for idx, row in potential_rows.iterrows():
            text = str(row['text'])
            if delimiter in text and not text.startswith(delimiter) and not text.endswith(delimiter):
                # Speciální pravidlo pro lomítko
                if delimiter == '/':
                    pos = text.find('/')
                    if pos <= 0 or pos >= len(text) - 1:
                        continue
                    if not (text[pos - 1].isalpha() and text[pos + 1].isalpha()):
                        continue

                parts = text.split(delimiter, 1)
                if len(parts) == 2 and parts[0].strip() and parts[1].strip():
                    all_rows_to_split.append((idx, row, delimiter))

    if not all_rows_to_split:
        return df

    def trim_char_list(chars: list[dict]) -> list[dict]:
        i, j = 0, len(chars) - 1
        while i <= j and str(chars[i]['char']).isspace():
            i += 1
        while j >= i and str(chars[j]['char']).isspace():
            j -= 1
        return chars[i:j + 1] if i <= j else []

    new_rows = []
    rows_to_remove = []

    for idx, row, delimiter in all_rows_to_split:
        text = str(row['text'])
        parts = text.split(delimiter, 1)
        if len(parts) != 2:
            continue
        before_delimiter = parts[0].strip()
        after_delimiter = parts[1].strip()
        if not before_delimiter or not after_delimiter:
            continue

        try:
            x, y, w, h = int(row['left']), int(row['top']), int(row['width']), int(row['height'])
            if image is None or h <= 0 or w <= 0:
                continue
            roi = image[y:y + h, x:x + w]

            if ocr_engine is None:
                continue

            boxes = ocr_engine.process_boxes(roi)
            if not boxes or 'char' not in boxes or 'left' not in boxes or 'right' not in boxes:
                continue

            count = len(boxes['char'])
            if count == 0:
                continue

            char_positions = []
            for i in range(count):
                try:
                    ch = boxes['char'][i]
                    left = int(boxes['left'][i])
                    right = int(boxes['right'][i])
                    top_b = int(boxes['top'][i]) if 'top' in boxes else 0
                    bottom_b = int(boxes['bottom'][i]) if 'bottom' in boxes else 0
                    top_tl = max(0, h - top_b)
                    bottom_tl = max(0, h - bottom_b)
                    char_positions.append({'char': ch, 'left': left, 'right': right,
                                           'top': top_tl, 'bottom': bottom_tl})
                except Exception:
                    continue

            if not char_positions:
                continue

            detected_text = ''.join([str(c['char']) for c in char_positions])
            delimiter_pos = detected_text.find(delimiter)
            if delimiter_pos == -1:
                continue

            delimiter_start_idx = delimiter_pos
            delimiter_end_idx = delimiter_pos + len(delimiter) - 1

            chars_before = char_positions[:delimiter_start_idx]
            chars_after = char_positions[delimiter_end_idx + 1:]

            chars_before = trim_char_list(chars_before)
            chars_after = trim_char_list(chars_after)

            if not chars_before or not chars_after:
                continue

            new_row_before = row.copy()
            new_row_before['text'] = ''.join([c['char'] for c in chars_before]).strip()
            left_before_roi = int(chars_before[0]['left'])
            right_before_roi = int(chars_before[-1]['right'])
            new_row_before['left'] = int(x + left_before_roi)
            new_row_before['width'] = int(max(1, right_before_roi - left_before_roi))
            new_row_before['level'] = 6

            new_row_after = row.copy()
            new_row_after['text'] = ''.join([c['char'] for c in chars_after]).strip()
            left_after_roi = int(chars_after[0]['left'])
            right_after_roi = int(chars_after[-1]['right'])
            new_row_after['left'] = int(x + left_after_roi)
            new_row_after['width'] = int(max(1, right_after_roi - left_after_roi))
            new_row_after['level'] = 6

            if new_row_before['text']:
                new_rows.append(new_row_before)
            if new_row_after['text']:
                new_rows.append(new_row_after)
            rows_to_remove.append(idx)
        except Exception as e:
            print(f"Error processing row {idx}: {e}")
            continue

    if rows_to_remove:
        df = df.drop(rows_to_remove)
    if new_rows:
        new_df = pd.DataFrame(new_rows)
        df = pd.concat([df, new_df], ignore_index=True)

    return df


def join_address(df: pd.DataFrame,
                 tol_line: float = 1.0,
                 tol_horiz_gap: int = 30,
                 tol_col: float = 2.5,
                 tol_vert_gap: float = 1.6) -> pd.DataFrame:
    """
    Krok 9: Spojování fragmentů adres/míst.
    """
    if df is None or df.empty:
        return df

    # Filtruj řádky s typem NER = adresa nebo místo
    address_mask = df['NER'].astype(str).str.contains(r'(adresa|místo)', case=False, regex=True)
    address_rows = df[address_mask].copy()
    if address_rows.empty:
        return df

    non_address_rows = df[~address_mask].copy()
    avg_height = address_rows['height'].mean()

    # Přidej pomocné sloupce
    address_rows['right'] = address_rows['left'] + address_rows['width']
    address_rows['bottom'] = address_rows['top'] + address_rows['height']

    visited = set()
    merged_addresses = []

    def find_chain(start_idx):
        chain = [start_idx]
        queue = [start_idx]

        while queue:
            cur_idx = queue.pop(0)
            if cur_idx in visited:
                continue
            visited.add(cur_idx)
            cur = address_rows.loc[cur_idx]

            for idx, row in address_rows.drop(index=visited, errors="ignore").iterrows():
                # horizontální soused
                same_line = abs(cur['top'] - row['top']) <= avg_height * tol_line
                horizontal_gap = abs(cur['right'] - row['left'])
                if same_line and horizontal_gap <= tol_horiz_gap:
                    if idx not in chain:
                        chain.append(idx)
                        queue.append(idx)

                # vertikální soused
                same_col = abs(cur['left'] - row['left']) <= avg_height * tol_col
                vertical_gap = abs(cur['bottom'] - row['top'])
                if same_col and vertical_gap <= avg_height * tol_vert_gap:
                    if idx not in chain:
                        chain.append(idx)
                        queue.append(idx)

        return chain

    for idx in address_rows.index:
        if idx in visited:
            continue

        cluster_indices = find_chain(idx)
        cluster_rows = address_rows.loc[cluster_indices]

        if len(cluster_rows) == 1:
            merged_addresses.append(cluster_rows.iloc[0])
        else:
            cluster_rows = cluster_rows.sort_values(by=['top', 'left'])

            merged_texts = []
            last_top = None
            for _, r in cluster_rows.iterrows():
                if last_top is not None and abs(r['top'] - last_top) > avg_height * tol_line:
                    merged_texts.append("\n")
                merged_texts.append(str(r['text']).strip())
                last_top = r['top']
            merged_text = " ".join(t for t in merged_texts if t).replace(" \n ", "\n")

            min_left = cluster_rows["left"].min()
            min_top = cluster_rows["top"].min()
            max_right = cluster_rows["right"].max()
            max_bottom = cluster_rows["bottom"].max()

            merged_row = cluster_rows.iloc[0].copy()
            merged_row['text'] = merged_text.strip()
            merged_row['left'] = min_left
            merged_row['top'] = min_top
            merged_row['width'] = max_right - min_left
            merged_row['height'] = max_bottom - min_top
            merged_row['right'] = max_right
            merged_row['bottom'] = max_bottom
            merged_row['NER_score'] = 0.9

            merged_addresses.append(merged_row)

    merged_df = pd.DataFrame(merged_addresses)
    result_df = pd.concat([non_address_rows, merged_df], ignore_index=True)

    return result_df.sort_values(by=['top', 'left']).reset_index(drop=True).drop(columns=['right', 'bottom'],
                                                                                 errors='ignore')