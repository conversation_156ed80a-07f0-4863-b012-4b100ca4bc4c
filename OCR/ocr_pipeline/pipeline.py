import cv2
import pandas as pd
from typing import Optional

#from semantic_classifier import key_classify
# Import modulů
from .cleaning import clean_and_normalize_initial, clean_basic_issues, clean_after_processing
from .filtering import filter_by_confidence_and_size
from .layout import find_and_mark_headers
from .text_processing import normalize_entities, merge_words, split_by_delimiter, join_address

# External dependencies
from NER import NER_test
from OCR.OcrEngine import OcrEngine
from OCR.OcrPreview import OcrPreviewQt
from utils.utils import classify_batch_values, postprocess


class OcrPipeline:
    """
    Hlavní orchestrační třída pro OCR pipeline.
    Koordinuje jednotlivé kroky zpracování pomocí modulárních funkcí.
    """

    def __init__(self, lang: str = "ces+eng", show_preview: bool = True):
        self.engine = OcrEngine(lang)
        self.image = None
        self.show_preview = show_preview

    def load_image(self, image: cv2.Mat) -> None:
        """Načte obrázek pro zpracování."""
        self.image = image

    def process(self, image: Optional[cv2.Mat] = None) -> pd.DataFrame:
        """
        Kompletní pipeline: OCR + čištění + spojování + klasifikace.

        Pořadí kroků:
        1. OCR (hotovo)
        2. Základní čištění a normalizace
        3. Filtrování podle confidence a velikosti
        4. Normalizace entit (Currency/Tax/ID mapping)
        5. NER
        6. Spojování slov
        7. Čištění po spojování (artefakty)
        8. Rozdělení podle oddělovačů
        9. Spojování adres
        10. Klasifikace typů
        11. Finální čištění (UNWANTED znaky z neklasifikovaných)
        """
        if image is not None:
            self.image = image

        if self.image is None:
            raise ValueError("No image provided for processing")

        # Krok 1: OCR
        df = self.engine.process_data(self.image)

        # Krok 2: Základní čištění a normalizace
        df = clean_and_normalize_initial(df)

        # Krok 3: Filtrování podle confidence a velikosti
        df = filter_by_confidence_and_size(df)

        # Krok 4: Normalizace entit
        df = normalize_entities(df)

        # Krok 5: NER
        # df = NER_test.ner_predict_row(df, threshold=0.55)

        # Krok 6: Spojování slov
        df = merge_words(df, gap_threshold=0.9)

        # Krok 7: Čištění po spojování (artefakty)
        df = clean_basic_issues(df)

        # Krok 8: Rozdělení podle oddělovačů
        df = split_by_delimiter(df, ocr_engine=self.engine, image=self.image)

        # Krok 8.1: Hledání a označování hlaviček
        df, _, _ = find_and_mark_headers(df, 5, 1)

        if hasattr(self, 'show_preview') and self.show_preview:
            preview = OcrPreviewQt(df, self.image)
            preview.show_and_wait()
        exit()
        # Krok 9: Spojování adres
        df = join_address(df)

        # Krok 10: Klasifikace typů
        df = classify_batch_values(df)

        # Krok 11: Finální čištění
        df = clean_after_processing(df)

        # Krok 12: Klasifikace klíčů
        df = key_classify.do(df)
        df.to_csv('debug.csv', index=False)
        df = postprocess(df)

        # Debug preview (volitelné)
        if hasattr(self, 'show_preview') and self.show_preview:
            preview = OcrPreviewQt(df, self.image)
            preview.show_and_wait()

        return df

    def set_preview(self, show: bool = True) -> None:
        """Nastaví, zda zobrazovat debug preview."""
        self.show_preview = show