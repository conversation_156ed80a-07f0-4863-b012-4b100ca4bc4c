import pandas as pd
from .constants import DEFAULT_CONFIDENCE_THRESHOLD, MIN_HEIGHT_THRESHOLD


def filter_by_confidence_and_size(df: pd.DataFrame,
                                  confidence_threshold: float = DEFAULT_CONFIDENCE_THRESHOLD,
                                  min_height: int = MIN_HEIGHT_THRESHOLD) -> pd.DataFrame:
    """
    Krok 3: Filtrování podle spolehlivosti a velikosti.
    - Odstraňuje řádky s confidence pod prahem (pouze pro level == 5)
    - Odstraňuje řádky s výškou pod prahem
    - Reindexace
    """

    print("Krok 3: Filtrování podle spolehlivosti a velikosti...")

    # Filtrování podle výšky
    #df = df[df["height"] > min_height].copy()

    # Filtrování podle confidence (pouze pro level == 5)
    if "level" in df.columns and "conf" in df.columns:
        mask = (df['level'] == 5)
        # Pro level==5 aplikujeme filtr na conf, ostatní ponecháme
        df = df[~mask | (df['conf'] >= confidence_threshold)].copy()

    # Reindexace
    df = df.reset_index(drop=True)

    return df